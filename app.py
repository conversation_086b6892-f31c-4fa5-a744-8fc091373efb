import streamlit as st
import requests
import json
import uuid
from datetime import datetime
import time
import pandas as pd
from typing import Optional, Dict, List, Any
import plotly.express as px
import plotly.graph_objects as go
from urllib.parse import urljoin
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "120"))

# Page configuration
st.set_page_config(
    page_title="AWS Cost Optimization Assistant",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #FF6B35, #F7931E);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: center;
        color: white;
    }
    
    .chat-message {
        background: #f0f2f6;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        border-left: 5px solid #FF6B35;
    }
    
    .user-message {
        background: #e3f2fd;
        border-left: 5px solid #2196f3;
    }
    
    .tool-execution {
        background: #f3e5f5;
        border: 1px solid #9c27b0;
        padding: 0.5rem;
        border-radius: 5px;
        margin: 0.5rem 0;
        font-size: 0.9em;
    }
    
    .success-tool {
        border-color: #4caf50;
        background: #e8f5e8;
    }
    
    .error-tool {
        border-color: #f44336;
        background: #ffebee;
    }
    
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 0.5rem 0;
    }
    
    .sidebar-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
    
    .stButton > button {
        background: linear-gradient(90deg, #FF6B35, #F7931E);
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: bold;
    }
    
    .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-healthy { background-color: #4caf50; }
    .status-error { background-color: #f44336; }
    .status-warning { background-color: #ff9800; }
    
    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 2rem;
    }
</style>
""", unsafe_allow_html=True)

# API Client Class
class APIClient:
    def __init__(self, base_url: str, timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request with error handling"""
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))
        
        try:
            response = requests.request(method, url, timeout=self.timeout, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.Timeout:
            st.error(f"Request timed out after {self.timeout} seconds")
            raise
        except requests.exceptions.ConnectionError:
            st.error(f"Unable to connect to the API server at {self.base_url}")
            raise
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 503:
                st.error("Service is initializing. Please wait a moment and try again.")
            else:
                st.error(f"API Error ({e.response.status_code}): {e.response.text}")
            raise
        except Exception as e:
            st.error(f"Unexpected error: {str(e)}")
            raise
    
    def health_check(self) -> Dict:
        """Check API health status"""
        response = self._make_request("GET", "/health")
        return response.json()
    
    def chat(self, message: str, session_id: Optional[str] = None) -> Dict:
        """Send chat message and get response"""
        payload = {"message": message}
        if session_id:
            payload["session_id"] = session_id
            
        response = self._make_request("POST", "/chat", json=payload)
        return response.json()
    
    def get_session_history(self, session_id: str) -> Dict:
        """Get conversation history for a session"""
        response = self._make_request("GET", f"/sessions/{session_id}/history")
        return response.json()
    
    def delete_session(self, session_id: str) -> Dict:
        """Delete a conversation session"""
        response = self._make_request("DELETE", f"/sessions/{session_id}")
        return response.json()
    
    def list_tools(self) -> Dict:
        """List available tools"""
        response = self._make_request("GET", "/tools")
        return response.json()

# Initialize API client
@st.cache_resource
def get_api_client():
    return APIClient(API_BASE_URL, API_TIMEOUT)

# Session state initialization
def initialize_session_state():
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
    
    if 'conversation_history' not in st.session_state:
        st.session_state.conversation_history = []
    
    if 'api_status' not in st.session_state:
        st.session_state.api_status = None
    
    if 'available_tools' not in st.session_state:
        st.session_state.available_tools = []

# Helper functions
def display_status_indicator(status: str, text: str):
    """Display status with colored indicator"""
    if status == "healthy":
        color_class = "status-healthy"
    elif status == "error":
        color_class = "status-error"
    else:
        color_class = "status-warning"
    
    st.markdown(f"""
    <div style="display: flex; align-items: center; margin: 0.5rem 0;">
        <span class="status-indicator {color_class}"></span>
        <span>{text}</span>
    </div>
    """, unsafe_allow_html=True)

def format_tool_executions(tool_executions: List[Dict]) -> str:
    """Format tool execution results for display"""
    if not tool_executions:
        return ""
    
    formatted = "\n\n### 🛠️ Tool Executions\n"
    
    for i, execution in enumerate(tool_executions, 1):
        status = execution.get('status', 'unknown')
        tool_name = execution.get('tool_name', 'Unknown Tool')
        
        if status == 'success':
            status_icon = "✅"
            css_class = "success-tool"
        elif status == 'error':
            status_icon = "❌"
            css_class = "error-tool"
        else:
            status_icon = "⏳"
            css_class = "tool-execution"
        
        formatted += f"""
<div class="tool-execution {css_class}">
    <strong>{status_icon} {tool_name}</strong><br>
    <small><strong>Arguments:</strong> {json.dumps(execution.get('arguments', {}), indent=2)}</small>
"""
        
        if execution.get('result'):
            formatted += f"<br><small><strong>Result:</strong> {execution['result'][:500]}{'...' if len(str(execution.get('result', ''))) > 500 else ''}</small>"
        
        if execution.get('error'):
            formatted += f"<br><small><strong>Error:</strong> {execution['error']}</small>"
        
        formatted += "</div>\n"
    
    return formatted

def display_metrics(response_data: Dict):
    """Display response metrics"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        processing_time = response_data.get('processing_time', 0)
        st.metric("⏱️ Processing Time", f"{processing_time:.2f}s")
    
    with col2:
        tokens_used = response_data.get('tokens_used') or 0
        st.metric("🎯 Tokens Used", f"{tokens_used:,}")
    
    with col3:
        tool_count = len(response_data.get('tool_executions', []))
        st.metric("🔧 Tools Used", tool_count)
    
    with col4:
        timestamp = response_data.get('timestamp', '')
        if timestamp:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            st.metric("🕐 Response Time", dt.strftime("%H:%M:%S"))

def main():
    # Initialize session state
    initialize_session_state()
    
    # Get API client
    api_client = get_api_client()
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>💰 AWS Cost Optimization Assistant</h1>
        <p>AI-powered AWS pricing analysis and cost optimization recommendations</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.markdown("### 🎛️ Control Panel")
        
        # API Status Section
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.markdown("#### 🔌 API Status")
        
        if st.button("🔄 Refresh Status"):
            try:
                with st.spinner("Checking API status..."):
                    status_data = api_client.health_check()
                    st.session_state.api_status = status_data
            except Exception as e:
                st.session_state.api_status = {"status": "error", "error": str(e)}
        
        if st.session_state.api_status:
            status = st.session_state.api_status.get('status', 'unknown')
            
            if status == 'healthy':
                display_status_indicator("healthy", "API Healthy")
                st.success(f"✅ {st.session_state.api_status.get('available_tools', 0)} tools available")
                st.info(f"📡 {st.session_state.api_status.get('active_servers', 0)} active servers")
            else:
                display_status_indicator("error", "API Error")
                error_msg = st.session_state.api_status.get('error', 'Unknown error')
                st.error(f"❌ {error_msg}")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Session Management
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.markdown("#### 💬 Session Management")
        
        st.text_input(
            "Session ID", 
            value=st.session_state.session_id[:8] + "...", 
            disabled=True,
            help="Current session identifier"
        )
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🆕 New Session"):
                st.session_state.session_id = str(uuid.uuid4())
                st.session_state.conversation_history = []
                st.rerun()
        
        with col2:
            if st.button("🗑️ Clear Chat"):
                st.session_state.conversation_history = []
                st.rerun()
        
        # Conversation stats
        if st.session_state.conversation_history:
            st.metric("💭 Messages", len(st.session_state.conversation_history))
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Tools Information
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.markdown("#### 🔧 Available Tools")
        
        if st.button("📋 Load Tools"):
            try:
                with st.spinner("Loading tools..."):
                    tools_data = api_client.list_tools()
                    st.session_state.available_tools = tools_data.get('tools', [])
            except Exception as e:
                st.error(f"Failed to load tools: {str(e)}")
        
        if st.session_state.available_tools:
            st.success(f"✅ {len(st.session_state.available_tools)} tools loaded")
            
            with st.expander("View Tools Details"):
                for tool in st.session_state.available_tools:
                    st.markdown(f"**{tool['name']}** ({tool['server']})")
                    st.caption(tool['description'][:100] + "...")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Quick Examples
        st.markdown('<div class="sidebar-section">', unsafe_allow_html=True)
        st.markdown("#### 💡 Quick Examples")
        
        examples = [
            "Compare EC2 pricing for t3.medium vs t3.large in us-east-1",
            "Calculate monthly costs for RDS db.r5.xlarge with 1TB storage",
            "Find the cheapest region for S3 storage",
            "Analyze cost savings with Reserved Instances",
            "Compare Lambda vs EC2 costs for my workload"
        ]
        
        for example in examples:
            if st.button(f"📝 {example[:30]}...", key=f"example_{hash(example)}", help=example):
                st.session_state.example_query = example
                st.rerun()
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Main chat interface
    st.markdown("### 💬 Chat with AWS Cost Optimizer")
    
    # Display conversation history
    if st.session_state.conversation_history:
        st.markdown("#### 📝 Conversation History")
        
        for i, message in enumerate(st.session_state.conversation_history):
            if message['type'] == 'user':
                st.markdown(f"""
                <div class="chat-message user-message">
                    <strong>🧑‍💻 You:</strong><br>
                    {message['content']}
                </div>
                """, unsafe_allow_html=True)
            
            elif message['type'] == 'assistant':
                st.markdown(f"""
                <div class="chat-message">
                    <strong>🤖 AWS Cost Optimizer:</strong><br>
                    {message['content']}
                </div>
                """, unsafe_allow_html=True)
                
                # Display tool executions if available
                if message.get('tool_executions'):
                    tool_html = format_tool_executions(message['tool_executions'])
                    st.markdown(tool_html, unsafe_allow_html=True)
                
                # Display metrics
                if message.get('response_data'):
                    with st.expander("📊 Response Metrics"):
                        display_metrics(message['response_data'])
    
    # Chat input
    chat_container = st.container()
    
    with chat_container:
        # Check if there's an example query to use
        default_query = ""
        if hasattr(st.session_state, 'example_query'):
            default_query = st.session_state.example_query
            delattr(st.session_state, 'example_query')
        
        with st.form(key="chat_form", clear_on_submit=True):
            user_input = st.text_area(
                "Your question:",
                value=default_query,
                placeholder="Ask me anything about AWS pricing and cost optimization...",
                height=100,
                help="Enter your AWS cost optimization question here"
            )
            
            col1, col2, col3 = st.columns([1, 1, 4])
            with col1:
                submit_button = st.form_submit_button("🚀 Send", type="primary")
            with col2:
                clear_button = st.form_submit_button("🧹 Clear")
            
            if clear_button:
                st.rerun()
        
        if submit_button and user_input.strip():
            # Add user message to history
            st.session_state.conversation_history.append({
                'type': 'user',
                'content': user_input,
                'timestamp': datetime.now().isoformat()
            })
            
            # Show loading indicator
            with st.spinner("🧠 AI is analyzing your request and executing tools..."):
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                try:
                    # Update progress
                    progress_bar.progress(25)
                    status_text.text("Connecting to AI assistant...")
                    
                    # Make API call
                    progress_bar.progress(50)
                    status_text.text("Processing your request...")
                    
                    response_data = api_client.chat(
                        message=user_input,
                        session_id=st.session_state.session_id
                    )
                    
                    progress_bar.progress(75)
                    status_text.text("Formatting response...")
                    
                    # Add assistant response to history
                    st.session_state.conversation_history.append({
                        'type': 'assistant',
                        'content': response_data.get('response', 'No response received'),
                        'tool_executions': response_data.get('tool_executions', []),
                        'response_data': response_data,
                        'timestamp': response_data.get('timestamp', datetime.now().isoformat())
                    })
                    
                    progress_bar.progress(100)
                    status_text.text("✅ Response received!")
                    
                    # Clean up progress indicators
                    time.sleep(1)
                    progress_bar.empty()
                    status_text.empty()
                    
                    # Rerun to display new messages
                    st.rerun()
                    
                except Exception as e:
                    progress_bar.empty()
                    status_text.empty()
                    
                    st.error(f"❌ Error: {str(e)}")
                    
                    # Add error message to history
                    st.session_state.conversation_history.append({
                        'type': 'assistant',
                        'content': f"I apologize, but I encountered an error: {str(e)}",
                        'timestamp': datetime.now().isoformat(),
                        'is_error': True
                    })
    
    # Footer with additional information
    st.markdown("---")
    
    with st.expander("ℹ️ How to use this tool"):
        st.markdown("""
        **AWS Cost Optimization Assistant** helps you:
        
        1. **Get Real-time Pricing**: Ask about current AWS service pricing
        2. **Compare Options**: Compare different instance types, regions, or services
        3. **Calculate Costs**: Get detailed cost breakdowns for your workloads
        4. **Optimize Spending**: Receive recommendations for cost savings
        5. **Analyze Scenarios**: Explore different architectural approaches
        
        **Example Questions:**
        - "What's the cost difference between t3.large and c5.large in us-west-2?"
        - "Calculate the monthly cost for a 100GB RDS MySQL instance"
        - "Which region has the lowest S3 storage costs?"
        - "Compare on-demand vs reserved instance pricing for my workload"
        - "Help me optimize costs for a web application with 1000 daily users"
        
        **Tips:**
        - Be specific about regions, instance types, and usage patterns
        - Ask follow-up questions to dive deeper into recommendations
        - Use the examples in the sidebar to get started
        """)
    
    # Display current session info at the bottom
    if st.session_state.conversation_history:
        st.markdown("---")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("💬 Session Messages", len(st.session_state.conversation_history))
        
        with col2:
            total_tools = sum(len(msg.get('tool_executions', [])) 
                            for msg in st.session_state.conversation_history 
                            if msg['type'] == 'assistant')
            st.metric("🔧 Total Tools Used", total_tools)
        
        with col3:
            if st.session_state.conversation_history:
                last_time = st.session_state.conversation_history[-1]['timestamp']
                dt = datetime.fromisoformat(last_time.replace('Z', '+00:00'))
                st.metric("🕐 Last Activity", dt.strftime("%H:%M:%S"))

if __name__ == "__main__":
    main()