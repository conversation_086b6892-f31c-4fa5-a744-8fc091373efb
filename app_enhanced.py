"""
Enhanced Streamlit Frontend with Context Retention
Provides a rich UI for the context-aware MCP bot
"""

import streamlit as st
import requests
import json
import uuid
from datetime import datetime
import time
import pandas as pd
from typing import Optional, Dict, List, Any
import plotly.express as px
import plotly.graph_objects as go
from urllib.parse import urljoin
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "120"))

# Page configuration
st.set_page_config(
    page_title="Enhanced AWS Cost Optimization Assistant",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced CSS for context-aware features
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #4CAF50, #2196F3);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: center;
        color: white;
    }
    
    .context-indicator {
        background: #e8f5e8;
        border: 2px solid #4caf50;
        padding: 0.5rem;
        border-radius: 8px;
        margin: 0.5rem 0;
        font-size: 0.9em;
    }
    
    .session-stats {
        background: #f0f8ff;
        border: 1px solid #2196f3;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
    
    .chat-message {
        background: #f0f2f6;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        border-left: 5px solid #FF6B35;
    }
    
    .user-message {
        background: #e3f2fd;
        border-left: 5px solid #2196f3;
    }
    
    .context-message {
        background: #e8f5e8;
        border-left: 5px solid #4caf50;
    }
    
    .tool-execution {
        background: #f3e5f5;
        border: 1px solid #9c27b0;
        padding: 0.5rem;
        border-radius: 5px;
        margin: 0.5rem 0;
        font-size: 0.9em;
    }
    
    .success-tool {
        border-color: #4caf50;
        background: #e8f5e8;
    }
    
    .error-tool {
        border-color: #f44336;
        background: #ffebee;
    }
</style>
""", unsafe_allow_html=True)

# Enhanced API Client Class
class EnhancedAPIClient:
    def __init__(self, base_url: str, timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request with error handling"""
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))
        
        try:
            response = requests.request(method, url, timeout=self.timeout, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.Timeout:
            st.error(f"Request timed out after {self.timeout} seconds")
            raise
        except requests.exceptions.ConnectionError:
            st.error(f"Unable to connect to the API server at {self.base_url}")
            raise
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 503:
                st.error("Service is initializing. Please wait a moment and try again.")
            else:
                st.error(f"API Error ({e.response.status_code}): {e.response.text}")
            raise
        except Exception as e:
            st.error(f"Unexpected error: {str(e)}")
            raise
    
    def health_check(self) -> Dict:
        """Check API health status with session stats"""
        response = self._make_request("GET", "/")
        return response.json()
    
    def chat_with_context(self, message: str, session_id: str) -> Dict:
        """Send chat message with context retention"""
        payload = {
            "message": message,
            "conversation_id": session_id,
            "use_tools": True
        }
        
        response = self._make_request("POST", "/chat", json=payload)
        return response.json()
    
    def get_session_history(self, session_id: str) -> Dict:
        """Get conversation history for a session"""
        response = self._make_request("GET", f"/sessions/{session_id}/history")
        return response.json()
    
    def get_session_stats(self, session_id: str) -> Dict:
        """Get detailed session statistics"""
        response = self._make_request("GET", f"/sessions/{session_id}/stats")
        return response.json()
    
    def clear_session(self, session_id: str) -> Dict:
        """Clear a session's conversation history"""
        response = self._make_request("DELETE", f"/sessions/{session_id}")
        return response.json()
    
    def list_sessions(self) -> Dict:
        """List all active sessions"""
        response = self._make_request("GET", "/sessions")
        return response.json()
    
    def list_tools(self) -> Dict:
        """List available tools"""
        response = self._make_request("GET", "/tools")
        return response.json()

# Initialize Enhanced API client
@st.cache_resource
def get_enhanced_api_client():
    return EnhancedAPIClient(API_BASE_URL, API_TIMEOUT)

# Enhanced session state initialization
def initialize_enhanced_session_state():
    if 'session_id' not in st.session_state:
        st.session_state.session_id = f"streamlit_{uuid.uuid4().hex[:12]}"
    
    if 'conversation_history' not in st.session_state:
        st.session_state.conversation_history = []
    
    if 'api_status' not in st.session_state:
        st.session_state.api_status = None
    
    if 'session_stats' not in st.session_state:
        st.session_state.session_stats = None
    
    if 'context_enabled' not in st.session_state:
        st.session_state.context_enabled = True
    
    if 'auto_load_history' not in st.session_state:
        st.session_state.auto_load_history = True

def display_context_indicator(context_used: bool, session_stats: Dict = None):
    """Display context retention indicator"""
    if context_used:
        st.markdown("""
        <div class="context-indicator">
            🧠 <strong>Context Active:</strong> This response used conversation history for better understanding
        </div>
        """, unsafe_allow_html=True)
    
    if session_stats:
        st.markdown(f"""
        <div class="session-stats">
            📊 <strong>Session Stats:</strong> 
            {session_stats.get('total_turns', 0)} turns, 
            {session_stats.get('total_tools_used', 0)} tools used, 
            {len(session_stats.get('servers_used', []))} servers active
        </div>
        """, unsafe_allow_html=True)

def display_enhanced_metrics(response_data: Dict):
    """Display enhanced response metrics with context info"""
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        tools_count = len(response_data.get('tools_used', []))
        st.metric("🔧 Tools Used", tools_count)
    
    with col2:
        context_used = response_data.get('context_used', False)
        st.metric("🧠 Context", "Yes" if context_used else "No")
    
    with col3:
        session_stats = response_data.get('session_stats', {})
        total_turns = session_stats.get('total_turns', 0)
        st.metric("💬 Total Turns", total_turns)
    
    with col4:
        total_tools = session_stats.get('total_tools_used', 0)
        st.metric("⚙️ Session Tools", total_tools)
    
    with col5:
        servers_used = len(session_stats.get('servers_used', []))
        st.metric("🖥️ Servers", servers_used)

def load_session_history():
    """Load conversation history from backend"""
    api_client = get_enhanced_api_client()
    
    try:
        history_data = api_client.get_session_history(st.session_state.session_id)
        
        # Convert backend history to Streamlit format
        st.session_state.conversation_history = []
        
        for turn in history_data.get('history', []):
            # Add user message
            st.session_state.conversation_history.append({
                'type': 'user',
                'content': turn['user_message'],
                'timestamp': turn['timestamp']
            })
            
            # Add assistant message
            st.session_state.conversation_history.append({
                'type': 'assistant',
                'content': turn['assistant_response'],
                'tools_used': turn['tools_used'],
                'timestamp': turn['timestamp'],
                'context_used': True  # Backend history always has context
            })
        
        st.session_state.session_stats = {
            'total_turns': history_data.get('message_count', 0),
            'total_tools_used': history_data.get('total_tools_used', 0),
            'context_summary': history_data.get('context_summary', '')
        }
        
        return True
        
    except Exception as e:
        st.error(f"Failed to load session history: {e}")
        return False

def main():
    # Initialize enhanced session state
    initialize_enhanced_session_state()
    
    # Get enhanced API client
    api_client = get_enhanced_api_client()
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🧠 Enhanced AWS Cost Optimization Assistant</h1>
        <p>AI-powered AWS analysis with conversation context and memory</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.markdown("### 🎛️ Enhanced Control Panel")
        
        # Context Settings
        st.markdown("#### 🧠 Context Settings")
        
        st.session_state.context_enabled = st.checkbox(
            "Enable Context Retention", 
            value=st.session_state.context_enabled,
            help="Maintain conversation context across messages"
        )
        
        st.session_state.auto_load_history = st.checkbox(
            "Auto-load Session History", 
            value=st.session_state.auto_load_history,
            help="Automatically load conversation history on startup"
        )
        
        # Session Management
        st.markdown("#### 💬 Session Management")
        
        st.text_input(
            "Session ID", 
            value=st.session_state.session_id,
            disabled=True,
            help="Current session identifier for context retention"
        )
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🆕 New Session"):
                st.session_state.session_id = f"streamlit_{uuid.uuid4().hex[:12]}"
                st.session_state.conversation_history = []
                st.session_state.session_stats = None
                st.rerun()
        
        with col2:
            if st.button("📥 Load History"):
                if load_session_history():
                    st.success("✅ History loaded!")
                    st.rerun()
        
        # Session Statistics
        if st.session_state.session_stats:
            st.markdown("#### 📊 Session Statistics")
            stats = st.session_state.session_stats
            
            st.metric("Total Turns", stats.get('total_turns', 0))
            st.metric("Tools Used", stats.get('total_tools_used', 0))
            
            if stats.get('context_summary'):
                st.info(f"Context: {stats['context_summary']}")
        
        # API Status
        st.markdown("#### 🔌 API Status")
        
        if st.button("🔄 Check Status"):
            try:
                with st.spinner("Checking enhanced API status..."):
                    status_data = api_client.health_check()
                    st.session_state.api_status = status_data
            except Exception as e:
                st.session_state.api_status = {"status": "error", "error": str(e)}
        
        if st.session_state.api_status:
            status = st.session_state.api_status.get('status', 'unknown')
            
            if status == 'healthy':
                st.success("✅ Enhanced API Healthy")
                
                session_stats = st.session_state.api_status.get('session_stats', {})
                if session_stats:
                    st.info(f"📡 {session_stats.get('total_sessions', 0)} active sessions")
                    st.info(f"🔧 {session_stats.get('total_tools_executed', 0)} total tools executed")
            else:
                st.error("❌ API Error")
                error_msg = st.session_state.api_status.get('error', 'Unknown error')
                st.error(f"Error: {error_msg}")
    
    # Main chat interface
    st.markdown("### 💬 Context-Aware Chat")
    
    # Auto-load history on startup
    if (st.session_state.auto_load_history and 
        not st.session_state.conversation_history and 
        'history_loaded' not in st.session_state):
        
        with st.spinner("Loading session history..."):
            load_session_history()
            st.session_state.history_loaded = True
    
    # Display conversation history
    if st.session_state.conversation_history:
        st.markdown("#### 📝 Conversation History")
        
        for i, message in enumerate(st.session_state.conversation_history):
            if message['type'] == 'user':
                st.markdown(f"""
                <div class="chat-message user-message">
                    <strong>🧑‍💻 You:</strong><br>
                    {message['content']}
                </div>
                """, unsafe_allow_html=True)
            
            elif message['type'] == 'assistant':
                css_class = "context-message" if message.get('context_used') else "chat-message"
                
                st.markdown(f"""
                <div class="chat-message {css_class}">
                    <strong>🤖 Enhanced Assistant:</strong><br>
                    {message['content']}
                </div>
                """, unsafe_allow_html=True)
                
                # Display context indicator
                if message.get('context_used'):
                    display_context_indicator(True)
                
                # Display tool executions
                if message.get('tools_used'):
                    with st.expander(f"🔧 Tools Used ({len(message['tools_used'])})"):
                        for tool in message['tools_used']:
                            status_icon = "✅" if tool.get('success') else "❌"
                            st.markdown(f"""
                            **{status_icon} {tool.get('tool_name', 'Unknown')}** 
                            (Server: {tool.get('server_name', 'Unknown')})
                            """)
                            
                            if tool.get('input'):
                                st.json(tool['input'])
    
    # Chat input
    with st.form(key="enhanced_chat_form", clear_on_submit=True):
        user_input = st.text_area(
            "Your question:",
            placeholder="Ask me anything about AWS costs. I'll remember our conversation context!",
            height=100,
            help="I can reference previous questions and build upon our conversation"
        )
        
        col1, col2, col3 = st.columns([1, 1, 4])
        with col1:
            submit_button = st.form_submit_button("🚀 Send", type="primary")
        with col2:
            clear_button = st.form_submit_button("🧹 Clear")
        
        if clear_button:
            try:
                api_client.clear_session(st.session_state.session_id)
                st.session_state.conversation_history = []
                st.session_state.session_stats = None
                st.success("✅ Session cleared!")
                st.rerun()
            except Exception as e:
                st.error(f"Failed to clear session: {e}")
    
    if submit_button and user_input.strip():
        # Add user message to history
        st.session_state.conversation_history.append({
            'type': 'user',
            'content': user_input,
            'timestamp': datetime.now().isoformat()
        })
        
        # Show loading indicator
        with st.spinner("🧠 Enhanced AI is analyzing with context..."):
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            try:
                # Update progress
                progress_bar.progress(25)
                status_text.text("Connecting to enhanced AI assistant...")
                
                # Make API call with context
                progress_bar.progress(50)
                status_text.text("Processing with conversation context...")
                
                response_data = api_client.chat_with_context(
                    message=user_input,
                    session_id=st.session_state.session_id
                )
                
                progress_bar.progress(75)
                status_text.text("Formatting contextual response...")
                
                # Add assistant response to history
                st.session_state.conversation_history.append({
                    'type': 'assistant',
                    'content': response_data.get('response', 'No response received'),
                    'tools_used': response_data.get('tools_used', []),
                    'context_used': response_data.get('context_used', False),
                    'timestamp': datetime.now().isoformat()
                })
                
                # Update session stats
                st.session_state.session_stats = response_data.get('session_stats')
                
                progress_bar.progress(100)
                status_text.text("✅ Contextual response received!")
                
                # Clean up progress indicators
                time.sleep(1)
                progress_bar.empty()
                status_text.empty()
                
                # Display enhanced metrics
                display_enhanced_metrics(response_data)
                
                # Rerun to display new messages
                st.rerun()
                
            except Exception as e:
                progress_bar.empty()
                status_text.empty()
                
                st.error(f"❌ Error: {str(e)}")
                
                # Add error message to history
                st.session_state.conversation_history.append({
                    'type': 'assistant',
                    'content': f"I apologize, but I encountered an error: {str(e)}",
                    'timestamp': datetime.now().isoformat(),
                    'is_error': True
                })

if __name__ == "__main__":
    main()
